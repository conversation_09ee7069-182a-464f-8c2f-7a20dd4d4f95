document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const csvFileUpload = document.getElementById('csvFileUpload');
    const uploadBtn = document.getElementById('uploadBtn');
    const loadingIndicator = document.getElementById('loadingIndicator');
    const errorMessage = document.getElementById('errorMessage');
    const dashboardContainer = document.getElementById('dashboardContainer');
    const totalFilesEl = document.getElementById('totalFiles');
    const totalTransactionsEl = document.getElementById('totalTransactions');
    const totalAmountEl = document.getElementById('totalAmount');
    const avgTransactionEl = document.getElementById('avgTransaction');
    const filesListEl = document.getElementById('filesList');
    const headerRow = document.getElementById('headerRow');
    const previewBody = document.getElementById('previewBody');
    
    // Summary elements
    const highestTransactionEl = document.getElementById('highestTransaction');
    const lowestTransactionEl = document.getElementById('lowestTransaction');
    const commonTransactionTypeEl = document.getElementById('commonTransactionType');
    const commonPaymentMethodEl = document.getElementById('commonPaymentMethod');
    const dateRangeEl = document.getElementById('dateRange');
    const uniqueCurrenciesEl = document.getElementById('uniqueCurrencies');
    
    // Serial statistics elements
    const totalSerialCountEl = document.getElementById('totalSerialCount');
    const totalUniqueSerialCountEl = document.getElementById('totalUniqueSerialCount');
    const totalCreditUniqueSerialEl = document.getElementById('totalCreditUniqueSerial');
    const totalDebitUniqueSerialEl = document.getElementById('totalDebitUniqueSerial');
    
    let allTransactionData = [];
    let fileStats = [];
    
    // Process CSV files on button click
    uploadBtn.addEventListener('click', function() {
        const files = csvFileUpload.files;
        if (files.length === 0) {
            showError('Please select at least one CSV file to upload.');
            return;
        }
        
        resetDashboard();
        showLoading();
        
        // Process all files
        processMultipleFiles(files);
    });
    
    // Function to process multiple CSV files
    async function processMultipleFiles(files) {
        allTransactionData = [];
        fileStats = [];
        
        try {
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                
                if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
                    throw new Error(`File "${file.name}" is not a valid CSV file.`);
                }
                
                console.log(`Processing file ${i + 1}/${files.length}: ${file.name}`);
                
                const fileData = await processSingleFile(file);
                allTransactionData.push(...fileData);
                
                // Store file statistics
                fileStats.push({
                    name: file.name,
                    recordCount: fileData.length,
                    totalAmount: fileData.reduce((sum, row) => sum + (parseFloat(row.TRANSACTION_AMOUNT) || 0), 0)
                });
            }
            
            if (allTransactionData.length === 0) {
                showError('No transaction data found in the uploaded files.');
                return;
            }
            
            analyzeCombinedData();
            hideLoading();
            dashboardContainer.classList.remove('hidden');
            
        } catch (error) {
            hideLoading();
            showError('Error processing files: ' + error.message);
        }
    }
    
    // Function to process a single CSV file
    function processSingleFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = function(event) {
                try {
                    const csvData = event.target.result;
                    const parsedData = parseCSV(csvData);
                    resolve(parsedData);
                } catch (error) {
                    reject(error);
                }
            };
            
            reader.onerror = function() {
                reject(new Error(`Error reading file: ${file.name}`));
            };
            
            reader.readAsText(file);
        });
    }
    
    // Function to parse CSV data
    function parseCSV(csvText) {
        console.log('CSV Text length:', csvText.length);
        
        // Split lines and handle different line endings
        const lines = csvText.split(/\r?\n/);
        console.log('Total lines:', lines.length);
        
        const result = [];
        
        if (lines.length < 2) {
            throw new Error('CSV file must contain at least a header row and one data row.');
        }
        
        // Extract header (first line) - handle both comma and tab separators
        let separator = ',';
        if (lines[0].includes('\t')) {
            separator = '\t';
        }
        
        const headers = lines[0].split(separator).map(header => header.trim().replace(/^"|"$/g, ''));
        console.log('Headers found:', headers.length);
        
        if (headers.length === 0) {
            throw new Error('No headers found in CSV file.');
        }
        
        // Process data rows
        let validRows = 0;
        for (let i = 1; i < lines.length; i++) {
            const line = lines[i].trim();
            if (line === '') continue;
            
            // Simple split approach first, then handle complex cases if needed
            let row;
            if (line.includes('"')) {
                // Handle quoted values with commas inside using a more robust parser
                row = parseCSVLine(line, separator);
            } else {
                // Simple split for non-quoted values
                row = line.split(separator).map(cell => cell.trim());
            }
            
            if (row.length === 0) continue;
            
            // Create object from headers and row values
            const rowObject = {};
            for (let j = 0; j < headers.length; j++) {
                const value = j < row.length ? row[j].replace(/^"|"$/g, '').trim() : '';
                rowObject[headers[j]] = value;
            }
            
            result.push(rowObject);
            validRows++;
        }
        
        console.log('Valid rows parsed:', validRows);
        return result;
    }
    
    // Helper function to parse a single CSV line with quoted values
    function parseCSVLine(line, separator = ',') {
        const result = [];
        let inQuotes = false;
        let currentValue = '';
        
        for (let i = 0; i < line.length; i++) {
            const char = line[i];
            const nextChar = i < line.length - 1 ? line[i + 1] : null;
            
            if (char === '"') {
                if (inQuotes && nextChar === '"') {
                    // Handle escaped quotes
                    currentValue += '"';
                    i++; // Skip next quote
                } else {
                    // Toggle quote state
                    inQuotes = !inQuotes;
                }
            } else if (char === separator && !inQuotes) {
                // End of field
                result.push(currentValue.trim());
                currentValue = '';
            } else {
                currentValue += char;
            }
        }
        
        // Add the last value
        result.push(currentValue.trim());
        
        return result;
    }
    
    // Function to analyze combined data from all files
    function analyzeCombinedData() {
        const totalFiles = fileStats.length;
        const totalTransactions = allTransactionData.length;
        
        let totalAmount = 0;
        let highestAmount = 0;
        let lowestAmount = Infinity;
        const transactionTypes = {};
        const paymentMethods = {};
        const currencies = new Set();
        const dates = [];
        
        // Serial number analysis
        const allSerialNumbers = [];
        const uniqueSerialNumbers = new Set();
        const creditSerialNumbers = new Set();
        const debitSerialNumbers = new Set();
        
        // Extract column names from first row
        const columns = Object.keys(allTransactionData[0] || {});
        
        // Analyze the combined data
        allTransactionData.forEach(transaction => {
            // Calculate amounts
            const amount = parseFloat(transaction.TRANSACTION_AMOUNT) || 0;
            totalAmount += amount;
            
            if (amount > highestAmount) highestAmount = amount;
            if (amount < lowestAmount && amount > 0) lowestAmount = amount;
            
            // Count by transaction type
            const type = transaction.TYPE_OF_BANK_TRANSACTION || 'Unknown';
            transactionTypes[type] = (transactionTypes[type] || 0) + 1;
            
            // Count by payment method
            const method = transaction.PAYMENT_METHOD || 'Unknown';
            paymentMethods[method] = (paymentMethods[method] || 0) + 1;
            
            // Collect currencies
            const currency = transaction.TRANSACTION_CURRENCY;
            if (currency) currencies.add(currency);
            
            // Collect dates for range calculation
            const date = transaction.TRANSACTION_DATE;
            if (date) dates.push(date);
            
            // Analyze serial numbers
            const serialNo = transaction.SERIAL_NO;
            const accountRole = transaction.ACCOUNT_HOLDER_ACCOUNT_ROLE;
            
            if (serialNo) {
                allSerialNumbers.push(serialNo);
                uniqueSerialNumbers.add(serialNo);
                
                // Check for credit/debit role
                if (accountRole === 'C') {
                    creditSerialNumbers.add(serialNo);
                } else if (accountRole === 'D') {
                    debitSerialNumbers.add(serialNo);
                }
            }
        });
        
        // Calculate average transaction amount
        const avgTransaction = totalTransactions > 0 ? totalAmount / totalTransactions : 0;
        
        // Find most common transaction type and payment method
        const mostCommonType = Object.keys(transactionTypes).reduce((a, b) => 
            transactionTypes[a] > transactionTypes[b] ? a : b, 'N/A');
        const mostCommonMethod = Object.keys(paymentMethods).reduce((a, b) => 
            paymentMethods[a] > paymentMethods[b] ? a : b, 'N/A');
        
        // Calculate date range
        let dateRange = 'N/A';
        if (dates.length > 0) {
            const sortedDates = dates.filter(d => d).sort();
            if (sortedDates.length > 0) {
                const firstDate = sortedDates[0];
                const lastDate = sortedDates[sortedDates.length - 1];
                dateRange = firstDate === lastDate ? firstDate : `${firstDate} to ${lastDate}`;
            }
        }
        
        // Update dashboard metrics
        totalFilesEl.textContent = totalFiles.toLocaleString();
        totalTransactionsEl.textContent = totalTransactions.toLocaleString();
        totalAmountEl.textContent = formatCurrency(totalAmount);
        avgTransactionEl.textContent = formatCurrency(avgTransaction);
        
        // Update summary
        highestTransactionEl.textContent = formatCurrency(highestAmount);
        lowestTransactionEl.textContent = lowestAmount === Infinity ? '$0' : formatCurrency(lowestAmount);
        commonTransactionTypeEl.textContent = mostCommonType;
        commonPaymentMethodEl.textContent = mostCommonMethod;
        dateRangeEl.textContent = dateRange;
        uniqueCurrenciesEl.textContent = currencies.size.toString();
        
        // Update serial statistics
        totalSerialCountEl.textContent = allSerialNumbers.length.toLocaleString();
        totalUniqueSerialCountEl.textContent = uniqueSerialNumbers.size.toLocaleString();
        totalCreditUniqueSerialEl.textContent = creditSerialNumbers.size.toLocaleString();
        totalDebitUniqueSerialEl.textContent = debitSerialNumbers.size.toLocaleString();
        
        // Display file statistics
        displayFileStatistics();
        
        // Create data preview table
        createDataPreview(allTransactionData, columns);
    }
    
    // Function to display file statistics
    function displayFileStatistics() {
        filesListEl.innerHTML = '';
        
        fileStats.forEach(file => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            
            const fileName = document.createElement('span');
            fileName.className = 'file-name';
            fileName.textContent = file.name;
            
            const fileStatsSpan = document.createElement('span');
            fileStatsSpan.className = 'file-stats';
            fileStatsSpan.textContent = `${file.recordCount.toLocaleString()} records | ${formatCurrency(file.totalAmount)}`;
            
            fileItem.appendChild(fileName);
            fileItem.appendChild(fileStatsSpan);
            filesListEl.appendChild(fileItem);
        });
    }
    
    // Function to create data preview table
    function createDataPreview(data, columns) {
        // Clear existing header and rows
        headerRow.innerHTML = '';
        previewBody.innerHTML = '';
        
        if (data.length === 0) return;
        
        // Add headers
        columns.forEach(column => {
            const th = document.createElement('th');
            th.textContent = column;
            headerRow.appendChild(th);
        });
        
        // Add data rows (first 10 only)
        const previewData = data.slice(0, 10);
        previewData.forEach(row => {
            const tr = document.createElement('tr');
            columns.forEach(column => {
                const td = document.createElement('td');
                td.textContent = row[column] || '';
                tr.appendChild(td);
            });
            previewBody.appendChild(tr);
        });
    }
    
    // Utility function to format currency
    function formatCurrency(amount) {
        return amount.toLocaleString('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2
        });
    }
    
    // Helper functions for UI states
    function showLoading() {
        loadingIndicator.classList.remove('hidden');
        errorMessage.classList.add('hidden');
    }
    
    function hideLoading() {
        loadingIndicator.classList.add('hidden');
    }
    
    function showError(message) {
        errorMessage.textContent = message;
        errorMessage.classList.remove('hidden');
        dashboardContainer.classList.add('hidden');
    }
    
    function resetDashboard() {
        // Reset metrics
        totalFilesEl.textContent = '0';
        totalTransactionsEl.textContent = '0';
        totalAmountEl.textContent = '0';
        avgTransactionEl.textContent = '0';
        
        // Reset summary
        highestTransactionEl.textContent = '$0';
        lowestTransactionEl.textContent = '$0';
        commonTransactionTypeEl.textContent = 'N/A';
        commonPaymentMethodEl.textContent = 'N/A';
        dateRangeEl.textContent = 'N/A';
        uniqueCurrenciesEl.textContent = '0';
        
        // Reset serial statistics
        totalSerialCountEl.textContent = '0';
        totalUniqueSerialCountEl.textContent = '0';
        totalCreditUniqueSerialEl.textContent = '0';
        totalDebitUniqueSerialEl.textContent = '0';
        
        // Reset file list and table
        filesListEl.innerHTML = '';
        headerRow.innerHTML = '';
        previewBody.innerHTML = '';
        
        // Hide dashboard
        dashboardContainer.classList.add('hidden');
        errorMessage.classList.add('hidden');
        
        // Reset data
        allTransactionData = [];
        fileStats = [];
    }
    
    console.log('Transaction Dashboard ready!');
});