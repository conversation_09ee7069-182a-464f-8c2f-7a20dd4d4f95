```markdown
# Project Summary
This project is a client-side web application designed to empower users to upload and analyze transaction data from multiple CSV files. It provides a comprehensive dashboard that summarizes key metrics, such as total transaction counts, amounts, and detailed serial number statistics, while ensuring all processing occurs locally in the user's browser for enhanced privacy and security. The application now includes a standalone HTML version for ease of use across different browsers without external dependencies.

# Project Module Description
The application consists of the following functional modules:
- **CSV Upload & Parsing**: Users can upload multiple CSV files simultaneously, with improved error handling and support for various delimiters.
- **Metrics Dashboard**: Displays essential metrics including total files processed, total transactions, combined amounts, average transaction amounts, and serial number statistics.
- **File Summary Section**: Shows a summary for each uploaded file, including record counts and total amounts.
- **Combined Data Preview**: Provides a preview of the first 10 rows from all uploaded CSV files combined.
- **Serial Number Statistics**: 
  1. **Total Serial Count**: Total count of all SERIAL_NO entries across all files.
  2. **Total Unique Serial Count**: Count of unique SERIAL_NO values.
  3. **Total Credit Unique Serial Numbers**: Unique SERIAL_NO count where ACCOUNT_HOLDER_ACCOUNT_ROLE = 'C'.
  4. **Total Debit Unique Serial Numbers**: Unique SERIAL_NO count where ACCOUNT_HOLDER_ACCOUNT_ROLE = 'D'.
- **Standalone HTML Version**: A self-contained HTML file (`transaction-dashboard-standalone.html`) that includes all CSS and JavaScript directly embedded, allowing offline usage and easy sharing.

# Directory Tree
```
html_template/
  ├── index.html                     # Main HTML file for the application
  ├── transaction-dashboard-standalone.html # Standalone HTML file for offline use
  ├── package.json                   # Project metadata and dependencies
  ├── script.js                      # JavaScript file containing the application logic
  ├── style.css                      # CSS file for styling the application
  └── template_config.json           # Configuration file for templates (currently not utilized)
uploads/
  └── clipboard.png                  # Placeholder image (currently not utilized)
```

# File Description Inventory
- **index.html**: The main interface of the application where users can upload CSV files and view the dashboard.
- **transaction-dashboard-standalone.html**: A self-contained HTML file for offline usage, including all necessary scripts and styles.
- **package.json**: Contains project dependencies and scripts for building and running the application.
- **script.js**: Implements core functionality for uploading, parsing multiple CSV files, and rendering the summary dashboard, including serial number statistics.
- **style.css**: Styles the application, ensuring a responsive and visually appealing layout, with specific styles for serial number statistics.
- **template_config.json**: Configuration file (not actively used in current implementation).
- **clipboard.png**: Placeholder image for potential future use.

# Technology Stack
- **HTML**: Markup language for structuring the web application.
- **CSS**: Styling language for visual presentation.
- **JavaScript**: Programming language for client-side logic and data processing.

# Usage
1. Clone the repository and navigate to the project directory.
2. Install dependencies using the command:
   ```
   pnpm install
   ```
3. Build the application (if necessary) using:
   ```
   pnpm run build
   ```
4. Open `index.html` in a web browser to start using the application.
5. For offline use, open `transaction-dashboard-standalone.html` directly in any web browser.
