<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="https://public-frontend-cos.metadl.com/mgx/img/favicon.png" type="image/png">
    <title>Transaction Data Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script type="module" crossorigin src="/assets/index--f2pxAO4.js"></script>
  <link rel="stylesheet" crossorigin href="/assets/index-aGOjj5ZW.css">
</head>

<body>
    <div class="container">
        <h1>Transaction Data Dashboard</h1>
        
        <div class="upload-section">
            <p>Upload your transaction CSV file to analyze the data</p>
            <input type="file" id="csvFileUpload" accept=".csv" />
            <button id="uploadBtn">Process Data</button>
        </div>
        
        <div id="loadingIndicator" class="loading-indicator hidden">
            <div class="spinner"></div>
            <p>Processing data...</p>
        </div>
        
        <div id="errorMessage" class="error-message hidden"></div>
        
        <div id="dashboardContainer" class="dashboard-container hidden">
            <div class="metrics-row">
                <div class="metric-card">
                    <h3>Total Transactions</h3>
                    <div id="totalTransactions" class="metric-value">0</div>
                </div>
                <div class="metric-card">
                    <h3>Total Amount</h3>
                    <div id="totalAmount" class="metric-value">0</div>
                </div>
                <div class="metric-card">
                    <h3>Average Transaction</h3>
                    <div id="avgTransaction" class="metric-value">0</div>
                </div>
            </div>
            
            <div class="charts-container">
                <div class="chart-box">
                    <h3>Transactions by Type</h3>
                    <canvas id="transactionTypeChart"></canvas>
                </div>
                <div class="chart-box">
                    <h3>Transactions by Payment Method</h3>
                    <canvas id="paymentMethodChart"></canvas>
                </div>
            </div>
            
            <div class="chart-box full-width">
                <h3>Transaction Amount Distribution</h3>
                <canvas id="amountDistributionChart"></canvas>
            </div>
            
            <div class="data-preview">
                <h3>Data Preview (First 10 rows)</h3>
                <div class="table-container">
                    <table id="dataPreview">
                        <thead>
                            <tr id="headerRow"></tr>
                        </thead>
                        <tbody id="previewBody"></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
</body>

</html>