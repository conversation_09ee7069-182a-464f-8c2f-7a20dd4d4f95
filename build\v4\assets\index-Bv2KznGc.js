(function(){const p=document.createElement("link").relList;if(p&&p.supports&&p.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))h(i);new MutationObserver(i=>{for(const d of i)if(d.type==="childList")for(const C of d.addedNodes)C.tagName==="LINK"&&C.rel==="modulepreload"&&h(C)}).observe(document,{childList:!0,subtree:!0});function N(i){const d={};return i.integrity&&(d.integrity=i.integrity),i.referrerPolicy&&(d.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?d.credentials="include":i.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function h(i){if(i.ep)return;i.ep=!0;const d=N(i);fetch(i.href,d)}})();document.addEventListener("DOMContentLoaded",function(){const O=document.getElementById("csvFileUpload"),p=document.getElementById("uploadBtn"),N=document.getElementById("loadingIndicator"),h=document.getElementById("errorMessage"),i=document.getElementById("dashboardContainer"),d=document.getElementById("totalFiles"),C=document.getElementById("totalTransactions"),v=document.getElementById("totalAmount"),D=document.getElementById("avgTransaction"),w=document.getElementById("filesList"),I=document.getElementById("headerRow"),A=document.getElementById("previewBody"),M=document.getElementById("highestTransaction"),R=document.getElementById("lowestTransaction"),b=document.getElementById("commonTransactionType"),U=document.getElementById("commonPaymentMethod"),F=document.getElementById("dateRange"),$=document.getElementById("uniqueCurrencies"),P=document.getElementById("totalSerialCount"),_=document.getElementById("totalUniqueSerialCount"),q=document.getElementById("totalCreditUniqueSerial"),H=document.getElementById("totalDebitUniqueSerial");let g=[],E=[];p.addEventListener("click",function(){const t=O.files;if(t.length===0){B("Please select at least one CSV file to upload.");return}ot(),nt(),W(t)});async function W(t){g=[],E=[];try{for(let e=0;e<t.length;e++){const o=t[e];if(o.type!=="text/csv"&&!o.name.endsWith(".csv"))throw new Error(`File "${o.name}" is not a valid CSV file.`);console.log(`Processing file ${e+1}/${t.length}: ${o.name}`);const n=await G(o);g.push(...n),E.push({name:o.name,recordCount:n.length,totalAmount:n.reduce((s,r)=>s+(parseFloat(r.TRANSACTION_AMOUNT)||0),0)})}if(g.length===0){B("No transaction data found in the uploaded files.");return}Z(),V(),i.classList.remove("hidden")}catch(e){V(),B("Error processing files: "+e.message)}}function G(t){return new Promise((e,o)=>{const n=new FileReader;n.onload=function(s){try{const r=s.target.result,l=J(r);e(l)}catch(r){o(r)}},n.onerror=function(){o(new Error(`Error reading file: ${t.name}`))},n.readAsText(t)})}function J(t){console.log("CSV Text length:",t.length);const e=t.split(/\r?\n/);console.log("Total lines:",e.length);const o=[];if(e.length<2)throw new Error("CSV file must contain at least a header row and one data row.");let n=",";e[0].includes("	")&&(n="	");const s=e[0].split(n).map(l=>l.trim().replace(/^"|"$/g,""));if(console.log("Headers found:",s.length),s.length===0)throw new Error("No headers found in CSV file.");let r=0;for(let l=1;l<e.length;l++){const m=e[l].trim();if(m==="")continue;let f;if(m.includes('"')?f=X(m,n):f=m.split(n).map(u=>u.trim()),f.length===0)continue;const S={};for(let u=0;u<s.length;u++){const x=u<f.length?f[u].replace(/^"|"$/g,"").trim():"";S[s[u]]=x}o.push(S),r++}return console.log("Valid rows parsed:",r),o}function X(t,e=","){const o=[];let n=!1,s="";for(let r=0;r<t.length;r++){const l=t[r],m=r<t.length-1?t[r+1]:null;l==='"'?n&&m==='"'?(s+='"',r++):n=!n:l===e&&!n?(o.push(s.trim()),s=""):s+=l}return o.push(s.trim()),o}function Z(){const t=E.length,e=g.length;let o=0,n=0,s=1/0;const r={},l={},m=new Set,f=[],S=[],u=new Set,x=new Set,z=new Set,st=Object.keys(g[0]||{});g.forEach(a=>{const c=parseFloat(a.TRANSACTION_AMOUNT)||0;o+=c,c>n&&(n=c),c<s&&c>0&&(s=c);const T=a.TYPE_OF_BANK_TRANSACTION||"Unknown";r[T]=(r[T]||0)+1;const Y=a.PAYMENT_METHOD||"Unknown";l[Y]=(l[Y]||0)+1;const K=a.TRANSACTION_CURRENCY;K&&m.add(K);const k=a.TRANSACTION_DATE;k&&f.push(k);const L=a.SERIAL_NO,Q=a.ACCOUNT_HOLDER_ACCOUNT_ROLE;L&&(S.push(L),u.add(L),Q==="C"?x.add(L):Q==="D"&&z.add(L))});const rt=e>0?o/e:0,lt=Object.keys(r).reduce((a,c)=>r[a]>r[c]?a:c,"N/A"),it=Object.keys(l).reduce((a,c)=>l[a]>l[c]?a:c,"N/A");let j="N/A";if(f.length>0){const a=f.filter(c=>c).sort();if(a.length>0){const c=a[0],T=a[a.length-1];j=c===T?c:`${c} to ${T}`}}d.textContent=t.toLocaleString(),C.textContent=e.toLocaleString(),v.textContent=y(o),D.textContent=y(rt),M.textContent=y(n),R.textContent=s===1/0?"$0":y(s),b.textContent=lt,U.textContent=it,F.textContent=j,$.textContent=m.size.toString(),P.textContent=S.length.toLocaleString(),_.textContent=u.size.toLocaleString(),q.textContent=x.size.toLocaleString(),H.textContent=z.size.toLocaleString(),tt(),et(g,st)}function tt(){w.innerHTML="",E.forEach(t=>{const e=document.createElement("div");e.className="file-item";const o=document.createElement("span");o.className="file-name",o.textContent=t.name;const n=document.createElement("span");n.className="file-stats",n.textContent=`${t.recordCount.toLocaleString()} records | ${y(t.totalAmount)}`,e.appendChild(o),e.appendChild(n),w.appendChild(e)})}function et(t,e){if(I.innerHTML="",A.innerHTML="",t.length===0)return;e.forEach(n=>{const s=document.createElement("th");s.textContent=n,I.appendChild(s)}),t.slice(0,10).forEach(n=>{const s=document.createElement("tr");e.forEach(r=>{const l=document.createElement("td");l.textContent=n[r]||"",s.appendChild(l)}),A.appendChild(s)})}function y(t){return t.toLocaleString("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2})}function nt(){N.classList.remove("hidden"),h.classList.add("hidden")}function V(){N.classList.add("hidden")}function B(t){h.textContent=t,h.classList.remove("hidden"),i.classList.add("hidden")}function ot(){d.textContent="0",C.textContent="0",v.textContent="0",D.textContent="0",M.textContent="$0",R.textContent="$0",b.textContent="N/A",U.textContent="N/A",F.textContent="N/A",$.textContent="0",P.textContent="0",_.textContent="0",q.textContent="0",H.textContent="0",w.innerHTML="",I.innerHTML="",A.innerHTML="",i.classList.add("hidden"),h.classList.add("hidden"),g=[],E=[]}console.log("Transaction Dashboard ready!")});
