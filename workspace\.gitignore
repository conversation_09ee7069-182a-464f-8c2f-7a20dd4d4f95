node_modules/
dist/
build/
out/
bin/
obj/
*.o
*.exe
*.log
logs/
.env
.env.local
.env.production
.env.development
env/
venv/
ENV/
.DS_Store
.idea/
*.iml
.vscode/
.vscode/extensions.json
.temp/
*.tmp
coverage/
CMakeFiles/
CMakeCache.txt
yarn-error.log
npm-debug.log
*~
*.bak
*.swp
Thumbs.db
ehthumbs.db
Desktop.ini
*.local
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.sass-cache/
.vite_opt_cache/
test-results/
*.coverage
*.lcov
*.user
*.code-workspace
pnpm-lock.yaml
*.pyc
__pycache__
