<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="https://public-frontend-cos.metadl.com/mgx/img/favicon.png" type="image/png">
    <title>Transaction Data Dashboard</title>
  <script type="module" crossorigin src="/assets/index-Bv2KznGc.js"></script>
  <link rel="stylesheet" crossorigin href="/assets/index-Vvv8PYmY.css">
</head>

<body>
    <div class="container">
        <h1>Transaction Data Dashboard</h1>
        
        <div class="upload-section">
            <p>Upload your transaction CSV files to analyze the data (you can select multiple files)</p>
            <input type="file" id="csvFileUpload" accept=".csv" multiple />
            <button id="uploadBtn">Process Data</button>
        </div>
        
        <div id="loadingIndicator" class="loading-indicator hidden">
            <div class="spinner"></div>
            <p>Processing data...</p>
        </div>
        
        <div id="errorMessage" class="error-message hidden"></div>
        
        <div id="dashboardContainer" class="dashboard-container hidden">
            <div class="file-summary-section">
                <h3>Files Processed</h3>
                <div id="filesList" class="files-list"></div>
                
                <div class="serial-stats-section">
                    <h4>Serial Number Statistics</h4>
                    <div class="serial-stats-grid">
                        <div class="serial-stat-item">
                            <span class="serial-label">Total Serial Count:</span>
                            <span id="totalSerialCount" class="serial-value">0</span>
                        </div>
                        <div class="serial-stat-item">
                            <span class="serial-label">Total Unique Serial Count:</span>
                            <span id="totalUniqueSerialCount" class="serial-value">0</span>
                        </div>
                        <div class="serial-stat-item">
                            <span class="serial-label">Total Credit Unique Serial Numbers:</span>
                            <span id="totalCreditUniqueSerial" class="serial-value">0</span>
                        </div>
                        <div class="serial-stat-item">
                            <span class="serial-label">Total Debit Unique Serial Numbers:</span>
                            <span id="totalDebitUniqueSerial" class="serial-value">0</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="metrics-row">
                <div class="metric-card">
                    <h3>Total Files</h3>
                    <div id="totalFiles" class="metric-value">0</div>
                </div>
                <div class="metric-card">
                    <h3>Total Transactions</h3>
                    <div id="totalTransactions" class="metric-value">0</div>
                </div>
                <div class="metric-card">
                    <h3>Total Amount</h3>
                    <div id="totalAmount" class="metric-value">0</div>
                </div>
                <div class="metric-card">
                    <h3>Average Transaction</h3>
                    <div id="avgTransaction" class="metric-value">0</div>
                </div>
            </div>
            
            <div class="summary-section">
                <h3>Transaction Summary</h3>
                <div class="summary-grid">
                    <div class="summary-item">
                        <span class="summary-label">Highest Transaction:</span>
                        <span id="highestTransaction" class="summary-value">$0</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Lowest Transaction:</span>
                        <span id="lowestTransaction" class="summary-value">$0</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Most Common Transaction Type:</span>
                        <span id="commonTransactionType" class="summary-value">N/A</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Most Common Payment Method:</span>
                        <span id="commonPaymentMethod" class="summary-value">N/A</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Date Range:</span>
                        <span id="dateRange" class="summary-value">N/A</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Unique Currencies:</span>
                        <span id="uniqueCurrencies" class="summary-value">0</span>
                    </div>
                </div>
            </div>
            
            <div class="data-preview">
                <h3>Combined Data Preview (First 10 rows)</h3>
                <div class="table-container">
                    <table id="dataPreview">
                        <thead>
                            <tr id="headerRow"></tr>
                        </thead>
                        <tbody id="previewBody"></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
</body>

</html>