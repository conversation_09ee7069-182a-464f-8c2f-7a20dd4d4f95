(function(){const h=document.createElement("link").relList;if(h&&h.supports&&h.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))d(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const u of i.addedNodes)u.tagName==="LINK"&&u.rel==="modulepreload"&&d(u)}).observe(document,{childList:!0,subtree:!0});function E(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function d(s){if(s.ep)return;s.ep=!0;const i=E(s);fetch(s.href,i)}})();document.addEventListener("DOMContentLoaded",function(){const x=document.getElementById("csvFileUpload"),h=document.getElementById("uploadBtn"),E=document.getElementById("loadingIndicator"),d=document.getElementById("errorMessage"),s=document.getElementById("dashboardContainer"),i=document.getElementById("totalTransactions"),u=document.getElementById("totalAmount"),O=document.getElementById("avgTransaction"),v=document.getElementById("headerRow"),T=document.getElementById("previewBody");let p=null,y=null,b=null;h.addEventListener("click",function(){const t=x.files[0];if(!t){C("Please select a CSV file to upload.");return}if(t.type!=="text/csv"&&!t.name.endsWith(".csv")){C("Please upload a valid CSV file.");return}F(),R(),setTimeout(()=>{I(t)},500)});function I(t){const e=new FileReader;e.onload=function(r){try{const n=r.target.result,o=D(n);if(o.length===0){C("No data found in the CSV file.");return}M(o),L(),s.classList.remove("hidden")}catch(n){L(),C("Error processing the CSV file: "+n.message)}},e.onerror=function(){L(),C("Error reading the file.")},e.readAsText(t)}function D(t){const e=t.split(/\\r?\\n/),r=[],n=e[0].split(",").map(o=>o.trim());for(let o=1;o<e.length;o++){if(e[o].trim()==="")continue;const c=[];let l=!1,f="";for(let a=0;a<e[o].length;a++){const g=e[o][a];g==='"'&&(a===0||e[o][a-1]!=="\\\\")?l=!l:g===","&&!l?(c.push(f.trim()),f=""):f+=g}c.push(f.trim());const m={};for(let a=0;a<n.length&&a<c.length;a++)m[n[a]]=c[a].replace(/^"|"$/g,"");r.push(m)}return r}function M(t){const e=t.length;let r=0;const n={},o={},c={"0-1000":0,"1001-5000":0,"5001-10000":0,"10001-50000":0,"50001-100000":0,"100001+":0},l=Object.keys(t[0]);t.forEach(m=>{const a=parseFloat(m.TRANSACTION_AMOUNT)||0;r+=a;const g=m.TYPE_OF_BANK_TRANSACTION||"Unknown";n[g]=(n[g]||0)+1;const B=m.PAYMENT_METHOD||"Unknown";o[B]=(o[B]||0)+1,a<=1e3?c["0-1000"]++:a<=5e3?c["1001-5000"]++:a<=1e4?c["5001-10000"]++:a<=5e4?c["10001-50000"]++:a<=1e5?c["50001-100000"]++:c["100001+"]++});const f=r/e;i.textContent=e.toLocaleString(),u.textContent=w(r),O.textContent=w(f),N(n),S(o),j(c),P(t,l)}function N(t){const e=document.getElementById("transactionTypeChart").getContext("2d");p&&p.destroy();const r=Object.keys(t),n=Object.values(t);p=new Chart(e,{type:"pie",data:{labels:r,datasets:[{data:n,backgroundColor:A(r.length),borderWidth:1}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"right"}}}})}function S(t){const e=document.getElementById("paymentMethodChart").getContext("2d");y&&y.destroy();const r=Object.keys(t),n=Object.values(t);y=new Chart(e,{type:"doughnut",data:{labels:r,datasets:[{data:n,backgroundColor:A(r.length),borderWidth:1}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"right"}}}})}function j(t){const e=document.getElementById("amountDistributionChart").getContext("2d");b&&b.destroy();const r=Object.keys(t),n=Object.values(t);b=new Chart(e,{type:"bar",data:{labels:r,datasets:[{label:"Number of Transactions",data:n,backgroundColor:"rgba(54, 162, 235, 0.5)",borderColor:"rgba(54, 162, 235, 1)",borderWidth:1}]},options:{responsive:!0,maintainAspectRatio:!1,scales:{y:{beginAtZero:!0,title:{display:!0,text:"Number of Transactions"}},x:{title:{display:!0,text:"Amount Range"}}}}})}function P(t,e){v.innerHTML="",T.innerHTML="",e.forEach(n=>{const o=document.createElement("th");o.textContent=n,v.appendChild(o)}),t.slice(0,10).forEach(n=>{const o=document.createElement("tr");e.forEach(c=>{const l=document.createElement("td");l.textContent=n[c]||"",o.appendChild(l)}),T.appendChild(o)})}function w(t){return t.toLocaleString("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2})}function A(t){const e=["rgba(54, 162, 235, 0.8)","rgba(255, 99, 132, 0.8)","rgba(255, 206, 86, 0.8)","rgba(75, 192, 192, 0.8)","rgba(153, 102, 255, 0.8)","rgba(255, 159, 64, 0.8)","rgba(199, 199, 199, 0.8)","rgba(83, 102, 255, 0.8)","rgba(40, 159, 64, 0.8)","rgba(210, 105, 30, 0.8)"],r=[];for(let n=0;n<t;n++)r.push(e[n%e.length]);return r}function R(){E.classList.remove("hidden"),d.classList.add("hidden")}function L(){E.classList.add("hidden")}function C(t){d.textContent=t,d.classList.remove("hidden"),s.classList.add("hidden")}function F(){p&&p.destroy(),y&&y.destroy(),b&&b.destroy(),i.textContent="0",u.textContent="0",O.textContent="0",v.innerHTML="",T.innerHTML="",s.classList.add("hidden"),d.classList.add("hidden")}console.log("Transaction Dashboard ready!")});
