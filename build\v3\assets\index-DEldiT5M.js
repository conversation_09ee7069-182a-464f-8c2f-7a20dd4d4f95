(function(){const C=document.createElement("link").relList;if(C&&C.supports&&C.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))g(a);new MutationObserver(a=>{for(const d of a)if(d.type==="childList")for(const p of d.addedNodes)p.tagName==="LINK"&&p.rel==="modulepreload"&&g(p)}).observe(document,{childList:!0,subtree:!0});function w(a){const d={};return a.integrity&&(d.integrity=a.integrity),a.referrerPolicy&&(d.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?d.credentials="include":a.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function g(a){if(a.ep)return;a.ep=!0;const d=w(a);fetch(a.href,d)}})();document.addEventListener("DOMContentLoaded",function(){const v=document.getElementById("csvFileUpload"),C=document.getElementById("uploadBtn"),w=document.getElementById("loadingIndicator"),g=document.getElementById("errorMessage"),a=document.getElementById("dashboardContainer"),d=document.getElementById("totalFiles"),p=document.getElementById("totalTransactions"),B=document.getElementById("totalAmount"),O=document.getElementById("avgTransaction"),A=document.getElementById("filesList"),N=document.getElementById("headerRow"),I=document.getElementById("previewBody"),M=document.getElementById("highestTransaction"),D=document.getElementById("lowestTransaction"),F=document.getElementById("commonTransactionType"),R=document.getElementById("commonPaymentMethod"),$=document.getElementById("dateRange"),P=document.getElementById("uniqueCurrencies");let h=[],y=[];C.addEventListener("click",function(){const t=v.files;if(t.length===0){x("Please select at least one CSV file to upload.");return}G(),W(),j(t)});async function j(t){h=[],y=[];try{for(let e=0;e<t.length;e++){const o=t[e];if(o.type!=="text/csv"&&!o.name.endsWith(".csv"))throw new Error(`File "${o.name}" is not a valid CSV file.`);console.log(`Processing file ${e+1}/${t.length}: ${o.name}`);const n=await q(o);h.push(...n),y.push({name:o.name,recordCount:n.length,totalAmount:n.reduce((s,r)=>s+(parseFloat(r.TRANSACTION_AMOUNT)||0),0)})}if(h.length===0){x("No transaction data found in the uploaded files.");return}K(),b(),a.classList.remove("hidden")}catch(e){b(),x("Error processing files: "+e.message)}}function q(t){return new Promise((e,o)=>{const n=new FileReader;n.onload=function(s){try{const r=s.target.result,c=Y(r);e(c)}catch(r){o(r)}},n.onerror=function(){o(new Error(`Error reading file: ${t.name}`))},n.readAsText(t)})}function Y(t){console.log("CSV Text length:",t.length);const e=t.split(/\r?\n/);console.log("Total lines:",e.length);const o=[];if(e.length<2)throw new Error("CSV file must contain at least a header row and one data row.");let n=",";e[0].includes("	")&&(n="	");const s=e[0].split(n).map(c=>c.trim().replace(/^"|"$/g,""));if(console.log("Headers found:",s.length),s.length===0)throw new Error("No headers found in CSV file.");let r=0;for(let c=1;c<e.length;c++){const u=e[c].trim();if(u==="")continue;let m;if(u.includes('"')?m=z(u,n):m=u.split(n).map(f=>f.trim()),m.length===0)continue;const L={};for(let f=0;f<s.length;f++){const S=f<m.length?m[f].replace(/^"|"$/g,"").trim():"";L[s[f]]=S}o.push(L),r++}return console.log("Valid rows parsed:",r),o}function z(t,e=","){const o=[];let n=!1,s="";for(let r=0;r<t.length;r++){const c=t[r],u=r<t.length-1?t[r+1]:null;c==='"'?n&&u==='"'?(s+='"',r++):n=!n:c===e&&!n?(o.push(s.trim()),s=""):s+=c}return o.push(s.trim()),o}function K(){const t=y.length,e=h.length;let o=0,n=0,s=1/0;const r={},c={},u=new Set,m=[],L=Object.keys(h[0]||{});h.forEach(l=>{const i=parseFloat(l.TRANSACTION_AMOUNT)||0;o+=i,i>n&&(n=i),i<s&&i>0&&(s=i);const T=l.TYPE_OF_BANK_TRANSACTION||"Unknown";r[T]=(r[T]||0)+1;const V=l.PAYMENT_METHOD||"Unknown";c[V]=(c[V]||0)+1;const H=l.TRANSACTION_CURRENCY;H&&u.add(H);const _=l.TRANSACTION_DATE;_&&m.push(_)});const f=e>0?o/e:0,S=Object.keys(r).reduce((l,i)=>r[l]>r[i]?l:i,"N/A"),J=Object.keys(c).reduce((l,i)=>c[l]>c[i]?l:i,"N/A");let U="N/A";if(m.length>0){const l=m.filter(i=>i).sort();if(l.length>0){const i=l[0],T=l[l.length-1];U=i===T?i:`${i} to ${T}`}}d.textContent=t.toLocaleString(),p.textContent=e.toLocaleString(),B.textContent=E(o),O.textContent=E(f),M.textContent=E(n),D.textContent=s===1/0?"$0":E(s),F.textContent=S,R.textContent=J,$.textContent=U,P.textContent=u.size.toString(),k(),Q(h,L)}function k(){A.innerHTML="",y.forEach(t=>{const e=document.createElement("div");e.className="file-item";const o=document.createElement("span");o.className="file-name",o.textContent=t.name;const n=document.createElement("span");n.className="file-stats",n.textContent=`${t.recordCount.toLocaleString()} records | ${E(t.totalAmount)}`,e.appendChild(o),e.appendChild(n),A.appendChild(e)})}function Q(t,e){if(N.innerHTML="",I.innerHTML="",t.length===0)return;e.forEach(n=>{const s=document.createElement("th");s.textContent=n,N.appendChild(s)}),t.slice(0,10).forEach(n=>{const s=document.createElement("tr");e.forEach(r=>{const c=document.createElement("td");c.textContent=n[r]||"",s.appendChild(c)}),I.appendChild(s)})}function E(t){return t.toLocaleString("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2})}function W(){w.classList.remove("hidden"),g.classList.add("hidden")}function b(){w.classList.add("hidden")}function x(t){g.textContent=t,g.classList.remove("hidden"),a.classList.add("hidden")}function G(){d.textContent="0",p.textContent="0",B.textContent="0",O.textContent="0",M.textContent="$0",D.textContent="$0",F.textContent="N/A",R.textContent="N/A",$.textContent="N/A",P.textContent="0",A.innerHTML="",N.innerHTML="",I.innerHTML="",a.classList.add("hidden"),g.classList.add("hidden"),h=[],y=[]}console.log("Transaction Dashboard ready!")});
