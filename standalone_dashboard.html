<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="https://public-frontend-cos.metadl.com/mgx/img/favicon.png" type="image/png">
    <title>Transaction Data Dashboard - Standalone</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f7f9fc;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }

        .upload-section {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
            margin-bottom: 30px;
        }

        input[type="file"] {
            display: block;
            margin: 20px auto;
            padding: 10px;
            width: 100%;
            max-width: 400px;
        }

        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            font-size: 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        button:hover {
            background-color: #2980b9;
        }

        .loading-indicator {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-left: 4px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin-bottom: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .progress-container {
            width: 100%;
            max-width: 500px;
            margin: 20px 0;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            border-radius: 10px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .progress-text {
            text-align: center;
            margin-top: 10px;
            font-weight: 500;
            color: #34495e;
        }

        .memory-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-size: 12px;
            color: #856404;
        }

        .hidden {
            display: none;
        }

        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            text-align: center;
        }

        .dashboard-container {
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }

        .metrics-row {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            flex: 1 1 250px;
            background-color: #f1f8fe;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
            text-align: center;
        }

        .metric-card h3 {
            margin-top: 0;
            color: #7f8c8d;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .metric-value {
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
        }

        .file-summary-section {
            margin-bottom: 30px;
        }

        .files-list {
            background-color: #f8f9fa;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
        }

        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .file-item:last-child {
            border-bottom: none;
        }

        .file-name {
            font-weight: 500;
            color: #2c3e50;
        }

        .file-stats {
            font-size: 14px;
            color: #7f8c8d;
        }

        .summary-section {
            margin-bottom: 30px;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #3498db;
        }

        .summary-label {
            font-weight: 500;
            color: #34495e;
        }

        .summary-value {
            font-weight: bold;
            color: #2c3e50;
        }

        .serial-stats-section {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }

        .serial-stats-section h4 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #34495e;
            font-size: 16px;
        }

        .serial-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 12px;
        }

        .serial-stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 12px;
            background-color: #fff5f5;
            border-radius: 4px;
            border-left: 4px solid #e74c3c;
        }

        .serial-label {
            font-weight: 500;
            color: #34495e;
            font-size: 14px;
        }

        .serial-value {
            font-weight: bold;
            color: #c0392b;
            font-size: 16px;
        }

        .data-preview {
            margin-top: 30px;
        }

        .data-preview h3 {
            color: #34495e;
            margin-bottom: 15px;
        }

        .table-container {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            border-spacing: 0;
            font-size: 14px;
        }

        thead {
            background-color: #f1f8fe;
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        th {
            color: #34495e;
            font-weight: 600;
        }

        tr:hover {
            background-color: #f8f9fa;
        }

        @media (max-width: 768px) {
            .metrics-row {
                flex-direction: column;
            }
            
            .charts-container {
                flex-direction: column;
            }
            
            .chart-box {
                flex: 1 1 100%;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>Transaction Data Dashboard</h1>
        
        <div class="upload-section">
            <p>Upload your transaction CSV files to analyze the data (you can select multiple files)</p>
            <input type="file" id="csvFileUpload" accept=".csv" multiple />
            <button id="uploadBtn">Process Data</button>
        </div>
        
        <div id="loadingIndicator" class="loading-indicator hidden">
            <div class="spinner"></div>
            <p id="loadingText">Processing data...</p>
            <div class="progress-container">
                <div class="progress-bar">
                    <div id="progressFill" class="progress-fill"></div>
                </div>
                <div id="progressText" class="progress-text">0%</div>
            </div>
            <div id="memoryInfo" class="memory-info">
                Memory usage: <span id="memoryUsage">0 MB</span> |
                Files processed: <span id="filesProcessed">0</span> |
                Batch: <span id="currentBatch">0</span>
            </div>
        </div>
        
        <div id="errorMessage" class="error-message hidden"></div>
        
        <div id="dashboardContainer" class="dashboard-container hidden">
            
            <div class="metrics-row">
                <div class="metric-card">
                    <h3>Total Files</h3>
                    <div id="totalFiles" class="metric-value">0</div>
                </div>
                <div class="metric-card">
                    <h3>Total Transactions</h3>
                    <div id="totalTransactions" class="metric-value">0</div>
                </div>
                <div class="metric-card">
                    <h3>Total Amount</h3>
                    <div id="totalAmount" class="metric-value">0</div>
                </div>
                <div class="metric-card">
                    <h3>Average Transaction</h3>
                    <div id="avgTransaction" class="metric-value">0</div>
                </div>
            </div>

            <div class="serial-stats-section">
                <h4>Serial Number Statistics</h4>
                <div class="serial-stats-grid">
                    <div class="serial-stat-item">
                        <span class="serial-label">Total Serial Count:</span>
                        <span id="totalSerialCount" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item">
                        <span class="serial-label">Total Unique Serial Count:</span>
                        <span id="totalUniqueSerialCount" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item">
                        <span class="serial-label">Total Credit Unique Serial Numbers:</span>
                        <span id="totalCreditUniqueSerial" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item">
                        <span class="serial-label">Total Debit Unique Serial Numbers:</span>
                        <span id="totalDebitUniqueSerial" class="serial-value">0</span>
                    </div>
                </div>
            </div>

            <div class="summary-section">
                <h3>Transaction Summary</h3>
                <div class="summary-grid">
                    <div class="summary-item">
                        <span class="summary-label">Highest Transaction:</span>
                        <span id="highestTransaction" class="summary-value">$0</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Lowest Transaction:</span>
                        <span id="lowestTransaction" class="summary-value">$0</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Most Common Transaction Type:</span>
                        <span id="commonTransactionType" class="summary-value">N/A</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Most Common Payment Method:</span>
                        <span id="commonPaymentMethod" class="summary-value">N/A</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Date Range:</span>
                        <span id="dateRange" class="summary-value">N/A</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Unique Currencies:</span>
                        <span id="uniqueCurrencies" class="summary-value">0</span>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <script>
        // Simplified debugger protection - prevent debugging interruptions
        (function() {
            // Basic debugger protection
            window.debugger = function() {};

            // Prevent memory crash popups
            const originalAlert = window.alert;
            const originalConfirm = window.confirm;

            window.alert = function(message) {
                if (typeof message === 'string' &&
                    (message.toLowerCase().includes('memory') ||
                     message.toLowerCase().includes('crash') ||
                     message.toLowerCase().includes('pause'))) {
                    console.log('Memory warning suppressed:', message);
                    return;
                }
                return originalAlert.call(this, message);
            };

            window.confirm = function(message) {
                if (typeof message === 'string' &&
                    (message.toLowerCase().includes('memory') ||
                     message.toLowerCase().includes('crash') ||
                     message.toLowerCase().includes('pause'))) {
                    console.log('Memory confirmation suppressed:', message);
                    return true; // Always continue
                }
                return originalConfirm.call(this, message);
            };

            // Prevent pausing on exceptions
            window.addEventListener('error', function(e) {
                console.log('Error caught:', e.message);
                e.preventDefault();
                return false;
            }, true);

            console.log('Basic protection enabled');
        })();

        // Keep page active to prevent Chrome optimizations
        setInterval(function() {
            try {
                // Simple activity simulation
                if (document.body) {
                    const dummy = document.createElement('span');
                    dummy.style.display = 'none';
                    document.body.appendChild(dummy);
                    document.body.removeChild(dummy);
                }
            } catch (e) {
                // Ignore errors
            }
        }, 5000);

        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM Content Loaded - Initializing dashboard...');

            // DOM Elements
            const csvFileUpload = document.getElementById('csvFileUpload');
            const uploadBtn = document.getElementById('uploadBtn');
            const loadingIndicator = document.getElementById('loadingIndicator');
            const errorMessage = document.getElementById('errorMessage');
            const dashboardContainer = document.getElementById('dashboardContainer');

            console.log('Key elements found:', {
                csvFileUpload: !!csvFileUpload,
                uploadBtn: !!uploadBtn,
                loadingIndicator: !!loadingIndicator,
                errorMessage: !!errorMessage,
                dashboardContainer: !!dashboardContainer
            });
            const totalFilesEl = document.getElementById('totalFiles');
            const totalTransactionsEl = document.getElementById('totalTransactions');
            const totalAmountEl = document.getElementById('totalAmount');
            const avgTransactionEl = document.getElementById('avgTransaction');
            const headerRow = document.getElementById('headerRow');
            const previewBody = document.getElementById('previewBody');

            // Progress and memory elements
            const loadingText = document.getElementById('loadingText');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            const memoryUsage = document.getElementById('memoryUsage');
            const filesProcessed = document.getElementById('filesProcessed');
            const currentBatch = document.getElementById('currentBatch');

            // Summary elements
            const highestTransactionEl = document.getElementById('highestTransaction');
            const lowestTransactionEl = document.getElementById('lowestTransaction');
            const commonTransactionTypeEl = document.getElementById('commonTransactionType');
            const commonPaymentMethodEl = document.getElementById('commonPaymentMethod');
            const dateRangeEl = document.getElementById('dateRange');
            const uniqueCurrenciesEl = document.getElementById('uniqueCurrencies');

            // Serial statistics elements
            const totalSerialCountEl = document.getElementById('totalSerialCount');
            const totalUniqueSerialCountEl = document.getElementById('totalUniqueSerialCount');
            const totalCreditUniqueSerialEl = document.getElementById('totalCreditUniqueSerial');
            const totalDebitUniqueSerialEl = document.getElementById('totalDebitUniqueSerial');

            // Ultra-aggressive memory management for 80+ files
            const MEMORY_CONFIG = {
                MAX_MEMORY_MB: 1000, // 1GB limit (very conservative)
                BATCH_SIZE: 3, // Process only 3 files at a time
                GC_THRESHOLD: 600, // Trigger GC at 600MB (very early)
                CHUNK_SIZE: 100, // Process only 100 rows at a time
                EMERGENCY_THRESHOLD: 800, // Emergency cleanup at 800MB
                MAX_ROWS_IN_MEMORY: 50000, // Never keep more than 50k rows in memory
                ANALYSIS_CHUNK_SIZE: 10000 // Analyze data in 10k chunks
            };

            // Use streaming approach - don't store all data in memory
            let fileStats = [];
            let memoryMonitor = null;
            let globalStats = {
                totalFiles: 0,
                totalTransactions: 0,
                totalAmount: 0,
                highestAmount: 0,
                lowestAmount: Infinity,
                transactionTypes: {},
                paymentMethods: {},
                currencies: new Set(),
                dates: [],
                allSerialNumbers: [],
                uniqueSerialNumbers: new Set(),
                creditSerialNumbers: new Set(),
                debitSerialNumbers: new Set(),
                sampleData: [] // Keep only first 10 rows for preview
            };

            // Enhanced Memory Guardian Functions
            function startMemoryMonitoring() {
                memoryMonitor = setInterval(() => {
                    try {
                        if (performance.memory) {
                            const memoryMB = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
                            updateMemoryDisplay(memoryMB);

                            // Emergency cleanup at higher threshold
                            if (memoryMB > MEMORY_CONFIG.EMERGENCY_THRESHOLD) {
                                console.log('Emergency memory cleanup triggered at', memoryMB, 'MB');
                                emergencyMemoryCleanup();
                                return true; // Continue but with emergency cleanup
                            }

                            // Regular cleanup
                            if (memoryMB > MEMORY_CONFIG.GC_THRESHOLD) {
                                console.log('Memory threshold reached, triggering cleanup...');
                                forceGarbageCollection();
                            }

                            // Never actually pause - just clean up more aggressively
                            if (memoryMB > MEMORY_CONFIG.MAX_MEMORY_MB) {
                                console.warn('Memory limit approached, aggressive cleanup...');
                                emergencyMemoryCleanup();
                                // Don't return false - keep processing but with cleanup
                            }
                        }
                        return true; // Always continue processing
                    } catch (e) {
                        // Ignore any errors in memory monitoring
                        console.log('Memory monitoring error ignored:', e.message);
                        return true;
                    }
                }, 500); // Check more frequently
            }

            function stopMemoryMonitoring() {
                if (memoryMonitor) {
                    clearInterval(memoryMonitor);
                    memoryMonitor = null;
                }
            }

            function forceGarbageCollection() {
                // Force garbage collection by creating and destroying large objects
                try {
                    // Multiple cleanup strategies
                    const temp1 = new Array(500000).fill(0);
                    temp1.length = 0;

                    const temp2 = new Array(500000).fill(null);
                    temp2.length = 0;

                    // Clear any temporary variables
                    if (window.gc) {
                        window.gc();
                    }

                    // Force browser to reclaim memory
                    if (window.CollectGarbage) {
                        window.CollectGarbage();
                    }
                } catch (e) {
                    // Ignore cleanup errors
                }
            }

            function emergencyMemoryCleanup() {
                try {
                    console.log('Emergency memory cleanup initiated...');

                    // Multiple aggressive cleanup attempts
                    for (let i = 0; i < 3; i++) {
                        forceGarbageCollection();
                    }

                    // Clear any large arrays or objects that might be hanging around
                    if (window.tempData) {
                        window.tempData = null;
                    }

                    // Force multiple GC cycles
                    setTimeout(() => forceGarbageCollection(), 100);
                    setTimeout(() => forceGarbageCollection(), 200);
                    setTimeout(() => forceGarbageCollection(), 300);

                    console.log('Emergency cleanup completed');
                } catch (e) {
                    // Ignore cleanup errors but continue
                    console.log('Emergency cleanup error ignored:', e.message);
                }
            }

            // Ultra-fast memory cleanup
            async function forceMemoryCleanup() {
                try {
                    // Quick cleanup
                    forceGarbageCollection();
                    await new Promise(resolve => setTimeout(resolve, 50));
                } catch (e) {
                    // Ignore errors
                }
            }

            // Major memory cleanup between batches
            async function majorMemoryCleanup() {
                try {
                    console.log('Major memory cleanup...');

                    // Clear any temporary variables
                    if (window.tempFileData) window.tempFileData = null;
                    if (window.tempRows) window.tempRows = null;

                    // Multiple GC cycles
                    for (let i = 0; i < 5; i++) {
                        forceGarbageCollection();
                        await new Promise(resolve => setTimeout(resolve, 100));
                    }

                    console.log('Major cleanup completed');
                } catch (e) {
                    console.log('Major cleanup error ignored:', e.message);
                }
            }

            // Stream process a single file without storing all data
            async function streamProcessFile(file) {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();

                    reader.onload = async function(event) {
                        try {
                            const csvData = event.target.result;
                            const result = await analyzeCSVStreaming(csvData);
                            resolve(result);
                        } catch (error) {
                            console.warn(`Error processing ${file.name}:`, error.message);
                            // Return empty result instead of failing
                            resolve({
                                recordCount: 0,
                                totalAmount: 0,
                                highestAmount: 0,
                                lowestAmount: Infinity,
                                transactionTypes: {},
                                paymentMethods: {},
                                currencies: [],
                                dates: [],
                                serialNumbers: [],
                                creditSerials: [],
                                debitSerials: [],
                                sampleRows: []
                            });
                        }
                    };

                    reader.onerror = function() {
                        console.warn(`Error reading file: ${file.name}`);
                        resolve({
                            recordCount: 0,
                            totalAmount: 0,
                            highestAmount: 0,
                            lowestAmount: Infinity,
                            transactionTypes: {},
                            paymentMethods: {},
                            currencies: [],
                            dates: [],
                            serialNumbers: [],
                            creditSerials: [],
                            debitSerials: [],
                            sampleRows: []
                        });
                    };

                    reader.readAsText(file);
                });
            }

            function updateMemoryDisplay(memoryMB) {
                if (memoryUsage) memoryUsage.textContent = `${memoryMB} MB`;

                // Color code memory usage
                if (memoryUsage) {
                    if (memoryMB > MEMORY_CONFIG.GC_THRESHOLD) {
                        memoryUsage.style.color = '#e74c3c';
                    } else if (memoryMB > MEMORY_CONFIG.MAX_MEMORY_MB * 0.7) {
                        memoryUsage.style.color = '#f39c12';
                    } else {
                        memoryUsage.style.color = '#27ae60';
                    }
                }
            }

            function updateProgress(current, total, text = '') {
                const percentage = Math.round((current / total) * 100);
                if (progressFill) progressFill.style.width = `${percentage}%`;
                if (progressText) progressText.textContent = `${percentage}%`;
                if (filesProcessed) filesProcessed.textContent = current;

                if (text && loadingText) loadingText.textContent = text;
            }

            // Process CSV files on button click
            uploadBtn.addEventListener('click', function() {
                console.log('Upload button clicked!');

                const files = csvFileUpload.files;
                console.log('Files selected:', files.length);

                if (files.length === 0) {
                    console.log('No files selected, showing error');
                    showError('Please select at least one CSV file to upload.');
                    return;
                }

                console.log('Starting file processing...');
                resetDashboard();
                showLoading();
                startMemoryMonitoring();

                // Process all files with memory management
                processMultipleFilesWithMemoryManagement(files);
            });

            // Ultra-fast streaming file processor for 80+ files
            async function processMultipleFilesWithMemoryManagement(files) {
                // Reset global stats
                globalStats = {
                    totalFiles: 0,
                    totalTransactions: 0,
                    totalAmount: 0,
                    highestAmount: 0,
                    lowestAmount: Infinity,
                    transactionTypes: {},
                    paymentMethods: {},
                    currencies: new Set(),
                    dates: [],
                    allSerialNumbers: [],
                    uniqueSerialNumbers: new Set(),
                    creditSerialNumbers: new Set(),
                    debitSerialNumbers: new Set(),
                    sampleData: []
                };
                fileStats = [];

                try {
                    const totalFiles = files.length;
                    let processedFiles = 0;
                    let currentBatchNum = 1;

                    console.log(`Starting ultra-fast processing of ${totalFiles} files...`);

                    // Process files in very small batches
                    for (let batchStart = 0; batchStart < totalFiles; batchStart += MEMORY_CONFIG.BATCH_SIZE) {
                        const batchEnd = Math.min(batchStart + MEMORY_CONFIG.BATCH_SIZE, totalFiles);
                        const batch = Array.from(files).slice(batchStart, batchEnd);

                        if (currentBatch) currentBatch.textContent = currentBatchNum;
                        updateProgress(processedFiles, totalFiles, `Batch ${currentBatchNum}/${Math.ceil(totalFiles/MEMORY_CONFIG.BATCH_SIZE)}`);

                        // Process current batch with streaming analysis
                        for (let i = 0; i < batch.length; i++) {
                            const file = batch[i];
                            const fileIndex = batchStart + i;

                            if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
                                console.warn(`Skipping non-CSV file: ${file.name}`);
                                processedFiles++;
                                continue;
                            }

                            updateProgress(fileIndex, totalFiles, `${file.name} (${Math.round(file.size/1024)}KB)`);

                            // Stream process file without storing all data
                            const fileResult = await streamProcessFile(file);

                            if (fileResult.recordCount > 0) {
                                // Update global stats incrementally
                                globalStats.totalFiles++;
                                globalStats.totalTransactions += fileResult.recordCount;
                                globalStats.totalAmount += fileResult.totalAmount;

                                if (fileResult.highestAmount > globalStats.highestAmount) {
                                    globalStats.highestAmount = fileResult.highestAmount;
                                }
                                if (fileResult.lowestAmount < globalStats.lowestAmount && fileResult.lowestAmount > 0) {
                                    globalStats.lowestAmount = fileResult.lowestAmount;
                                }

                                // Merge stats
                                Object.keys(fileResult.transactionTypes).forEach(type => {
                                    globalStats.transactionTypes[type] = (globalStats.transactionTypes[type] || 0) + fileResult.transactionTypes[type];
                                });

                                Object.keys(fileResult.paymentMethods).forEach(method => {
                                    globalStats.paymentMethods[method] = (globalStats.paymentMethods[method] || 0) + fileResult.paymentMethods[method];
                                });

                                fileResult.currencies.forEach(currency => globalStats.currencies.add(currency));
                                globalStats.dates.push(...fileResult.dates);

                                // Serial number stats
                                fileResult.serialNumbers.forEach(serial => {
                                    globalStats.allSerialNumbers.push(serial);
                                    globalStats.uniqueSerialNumbers.add(serial);
                                });
                                fileResult.creditSerials.forEach(serial => globalStats.creditSerialNumbers.add(serial));
                                fileResult.debitSerials.forEach(serial => globalStats.debitSerialNumbers.add(serial));

                                // Keep only first few rows for preview
                                if (globalStats.sampleData.length < 10) {
                                    globalStats.sampleData.push(...fileResult.sampleRows.slice(0, 10 - globalStats.sampleData.length));
                                }

                                fileStats.push({
                                    name: file.name,
                                    recordCount: fileResult.recordCount,
                                    totalAmount: fileResult.totalAmount
                                });
                            }

                            processedFiles++;

                            // Aggressive memory cleanup after each file
                            if (processedFiles % 5 === 0) { // Every 5 files
                                updateProgress(processedFiles, totalFiles, 'Memory cleanup...');
                                await forceMemoryCleanup();
                            }
                        }

                        // Major cleanup between batches
                        updateProgress(processedFiles, totalFiles, 'Batch cleanup...');
                        await majorMemoryCleanup();

                        currentBatchNum++;
                    }

                    if (globalStats.totalTransactions === 0) {
                        showError('No transaction data found in the uploaded files.');
                        return;
                    }

                    updateProgress(totalFiles, totalFiles, 'Finalizing results...');
                    await new Promise(resolve => setTimeout(resolve, 100));

                    displayStreamingResults();
                    stopMemoryMonitoring();
                    hideLoading();
                    dashboardContainer.classList.remove('hidden');

                } catch (error) {
                    // Never let errors completely stop the application
                    console.log('Processing error caught:', error.message);

                    try {
                        // Attempt to continue with whatever data we have
                        if (allTransactionData.length > 0) {
                            updateProgress(totalFiles, totalFiles, 'Completing with available data...');
                            analyzeCombinedData();
                            stopMemoryMonitoring();
                            hideLoading();
                            dashboardContainer.classList.remove('hidden');

                            // Show a warning but don't stop
                            const warningDiv = document.createElement('div');
                            warningDiv.style.cssText = 'background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; border-radius: 4px; color: #856404;';
                            warningDiv.textContent = `Warning: Some files may not have been fully processed due to: ${error.message}`;
                            dashboardContainer.insertBefore(warningDiv, dashboardContainer.firstChild);
                        } else {
                            // Only show error if we have no data at all
                            stopMemoryMonitoring();
                            hideLoading();
                            showError('Error processing files: ' + error.message);
                        }
                    } catch (recoveryError) {
                        // Last resort - just show the original error
                        console.log('Recovery failed:', recoveryError.message);
                        stopMemoryMonitoring();
                        hideLoading();
                        showError('Error processing files: ' + error.message);
                    }
                }
            }

            // Function to process a single CSV file with chunking
            function processSingleFileWithChunking(file) {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();

                    reader.onload = async function(event) {
                        try {
                            const csvData = event.target.result;
                            const parsedData = await parseCSVWithChunking(csvData);
                            resolve(parsedData);
                        } catch (error) {
                            reject(error);
                        }
                    };

                    reader.onerror = function() {
                        reject(new Error(`Error reading file: ${file.name}`));
                    };

                    reader.readAsText(file);
                });
            }

            // Legacy function for backward compatibility
            function processSingleFile(file) {
                return processSingleFileWithChunking(file);
            }

            // Function to parse CSV data with chunking for memory management
            async function parseCSVWithChunking(csvText) {
                console.log('CSV Text length:', csvText.length);

                // Split lines and handle different line endings
                const lines = csvText.split(/\r?\n/);
                console.log('Total lines:', lines.length);

                const result = [];

                if (lines.length < 2) {
                    throw new Error('CSV file must contain at least a header row and one data row.');
                }

                // Extract header (first line) - handle both comma and tab separators
                let separator = ',';
                if (lines[0].includes('\t')) {
                    separator = '\t';
                }

                const headers = lines[0].split(separator).map(header => header.trim().replace(/^"|"$/g, ''));
                console.log('Headers found:', headers.length);

                if (headers.length === 0) {
                    throw new Error('No headers found in CSV file.');
                }

                // Process data rows in chunks
                let validRows = 0;
                const totalDataLines = lines.length - 1;

                for (let chunkStart = 1; chunkStart < lines.length; chunkStart += MEMORY_CONFIG.CHUNK_SIZE) {
                    const chunkEnd = Math.min(chunkStart + MEMORY_CONFIG.CHUNK_SIZE, lines.length);
                    const chunk = [];

                    for (let i = chunkStart; i < chunkEnd; i++) {
                        const line = lines[i].trim();
                        if (line === '') continue;

                        // Simple split approach first, then handle complex cases if needed
                        let row;
                        if (line.includes('"')) {
                            // Handle quoted values with commas inside using a more robust parser
                            row = parseCSVLine(line, separator);
                        } else {
                            // Simple split for non-quoted values
                            row = line.split(separator).map(cell => cell.trim());
                        }

                        if (row.length === 0) continue;

                        // Create object from headers and row values
                        const rowObject = {};
                        for (let j = 0; j < headers.length; j++) {
                            const value = j < row.length ? row[j].replace(/^"|"$/g, '').trim() : '';
                            rowObject[headers[j]] = value;
                        }

                        chunk.push(rowObject);
                        validRows++;
                    }

                    // Add chunk to result
                    result.push(...chunk);

                    // Allow UI updates and memory cleanup between chunks
                    if (chunkEnd < lines.length) {
                        await new Promise(resolve => setTimeout(resolve, 10));
                    }
                }

                console.log('Valid rows parsed:', validRows);
                return result;
            }

            // Legacy function for backward compatibility
            function parseCSV(csvText) {
                return parseCSVWithChunking(csvText);
            }

            // Ultra-fast streaming CSV analyzer - analyzes without storing all data
            async function analyzeCSVStreaming(csvText) {
                const lines = csvText.split(/\r?\n/);

                if (lines.length < 2) {
                    throw new Error('CSV file must contain at least a header row and one data row.');
                }

                // Extract header
                let separator = ',';
                if (lines[0].includes('\t')) {
                    separator = '\t';
                }

                const headers = lines[0].split(separator).map(header => header.trim().replace(/^"|"$/g, ''));

                if (headers.length === 0) {
                    throw new Error('No headers found in CSV file.');
                }

                // Initialize result object
                const result = {
                    recordCount: 0,
                    totalAmount: 0,
                    highestAmount: 0,
                    lowestAmount: Infinity,
                    transactionTypes: {},
                    paymentMethods: {},
                    currencies: [],
                    dates: [],
                    serialNumbers: [],
                    creditSerials: [],
                    debitSerials: [],
                    sampleRows: []
                };

                const currencySet = new Set();
                let sampleCount = 0;

                // Process data rows in small chunks without storing everything
                for (let i = 1; i < lines.length; i += MEMORY_CONFIG.CHUNK_SIZE) {
                    const chunkEnd = Math.min(i + MEMORY_CONFIG.CHUNK_SIZE, lines.length);

                    for (let j = i; j < chunkEnd; j++) {
                        const line = lines[j].trim();
                        if (line === '') continue;

                        let row;
                        if (line.includes('"')) {
                            row = parseCSVLine(line, separator);
                        } else {
                            row = line.split(separator).map(cell => cell.trim());
                        }

                        if (row.length === 0) continue;

                        // Create row object
                        const rowObject = {};
                        for (let k = 0; k < headers.length; k++) {
                            const value = k < row.length ? row[k].replace(/^"|"$/g, '').trim() : '';
                            rowObject[headers[k]] = value;
                        }

                        // Analyze this row immediately (streaming analysis)
                        result.recordCount++;

                        const amount = parseFloat(rowObject.TRANSACTION_AMOUNT) || 0;
                        result.totalAmount += amount;

                        if (amount > result.highestAmount) result.highestAmount = amount;
                        if (amount < result.lowestAmount && amount > 0) result.lowestAmount = amount;

                        // Count transaction types
                        const type = rowObject.TYPE_OF_BANK_TRANSACTION || 'Unknown';
                        result.transactionTypes[type] = (result.transactionTypes[type] || 0) + 1;

                        // Count payment methods
                        const method = rowObject.PAYMENT_METHOD || 'Unknown';
                        result.paymentMethods[method] = (result.paymentMethods[method] || 0) + 1;

                        // Collect currencies
                        const currency = rowObject.TRANSACTION_CURRENCY;
                        if (currency) currencySet.add(currency);

                        // Collect dates
                        const date = rowObject.TRANSACTION_DATE;
                        if (date) result.dates.push(date);

                        // Serial number analysis
                        const serialNo = rowObject.SERIAL_NO;
                        const accountRole = rowObject.ACCOUNT_HOLDER_ACCOUNT_ROLE;

                        if (serialNo) {
                            result.serialNumbers.push(serialNo);

                            if (accountRole === 'C') {
                                result.creditSerials.push(serialNo);
                            } else if (accountRole === 'D') {
                                result.debitSerials.push(serialNo);
                            }
                        }

                        // Keep only first few rows for preview
                        if (sampleCount < 10) {
                            result.sampleRows.push(rowObject);
                            sampleCount++;
                        }
                    }

                    // Allow UI updates and memory cleanup between chunks
                    if (chunkEnd < lines.length) {
                        await new Promise(resolve => setTimeout(resolve, 1));
                    }
                }

                result.currencies = Array.from(currencySet);
                return result;
            }

            // Helper function to parse a single CSV line with quoted values
            function parseCSVLine(line, separator = ',') {
                const result = [];
                let inQuotes = false;
                let currentValue = '';

                for (let i = 0; i < line.length; i++) {
                    const char = line[i];
                    const nextChar = i < line.length - 1 ? line[i + 1] : null;

                    if (char === '"') {
                        if (inQuotes && nextChar === '"') {
                            // Handle escaped quotes
                            currentValue += '"';
                            i++; // Skip next quote
                        } else {
                            // Toggle quote state
                            inQuotes = !inQuotes;
                        }
                    } else if (char === separator && !inQuotes) {
                        // End of field
                        result.push(currentValue.trim());
                        currentValue = '';
                    } else {
                        currentValue += char;
                    }
                }

                // Add the last value
                result.push(currentValue.trim());

                return result;
            }

            // Display results from streaming analysis
            function displayStreamingResults() {
                console.log('Displaying streaming results...');

                // Calculate average transaction amount
                const avgTransaction = globalStats.totalTransactions > 0 ? globalStats.totalAmount / globalStats.totalTransactions : 0;

                // Find most common transaction type and payment method
                const mostCommonType = Object.keys(globalStats.transactionTypes).reduce((a, b) =>
                    globalStats.transactionTypes[a] > globalStats.transactionTypes[b] ? a : b, 'N/A');
                const mostCommonMethod = Object.keys(globalStats.paymentMethods).reduce((a, b) =>
                    globalStats.paymentMethods[a] > globalStats.paymentMethods[b] ? a : b, 'N/A');

                // Calculate date range
                let dateRange = 'N/A';
                if (globalStats.dates.length > 0) {
                    const sortedDates = globalStats.dates.filter(d => d).sort();
                    if (sortedDates.length > 0) {
                        const firstDate = sortedDates[0];
                        const lastDate = sortedDates[sortedDates.length - 1];
                        dateRange = firstDate === lastDate ? firstDate : `${firstDate} to ${lastDate}`;
                    }
                }

                // Update dashboard metrics
                if (totalFilesEl) totalFilesEl.textContent = globalStats.totalFiles.toLocaleString();
                if (totalTransactionsEl) totalTransactionsEl.textContent = globalStats.totalTransactions.toLocaleString();
                if (totalAmountEl) totalAmountEl.textContent = formatCurrency(globalStats.totalAmount);
                if (avgTransactionEl) avgTransactionEl.textContent = formatCurrency(avgTransaction);

                // Update summary
                if (highestTransactionEl) highestTransactionEl.textContent = formatCurrency(globalStats.highestAmount);
                if (lowestTransactionEl) lowestTransactionEl.textContent = globalStats.lowestAmount === Infinity ? '$0' : formatCurrency(globalStats.lowestAmount);
                if (commonTransactionTypeEl) commonTransactionTypeEl.textContent = mostCommonType;
                if (commonPaymentMethodEl) commonPaymentMethodEl.textContent = mostCommonMethod;
                if (dateRangeEl) dateRangeEl.textContent = dateRange;
                if (uniqueCurrenciesEl) uniqueCurrenciesEl.textContent = globalStats.currencies.size.toString();

                // Update serial statistics
                if (totalSerialCountEl) totalSerialCountEl.textContent = globalStats.allSerialNumbers.length.toLocaleString();
                if (totalUniqueSerialCountEl) totalUniqueSerialCountEl.textContent = globalStats.uniqueSerialNumbers.size.toLocaleString();
                if (totalCreditUniqueSerialEl) totalCreditUniqueSerialEl.textContent = globalStats.creditSerialNumbers.size.toLocaleString();
                if (totalDebitUniqueSerialEl) totalDebitUniqueSerialEl.textContent = globalStats.debitSerialNumbers.size.toLocaleString();

                // Display file statistics
                // displayFileStatistics(); // This function is removed

                // Create data preview table from sample data
                if (globalStats.sampleData.length > 0) {
                    const columns = Object.keys(globalStats.sampleData[0]);
                    createDataPreview(globalStats.sampleData, columns);
                }

                console.log('Results displayed successfully');
            }

            // Legacy function for backward compatibility
            function analyzeCombinedData() {
                displayStreamingResults();
            }

            // Function to create data preview table
            function createDataPreview(data, columns) {
                // Clear existing header and rows
                if (headerRow) headerRow.innerHTML = '';
                if (previewBody) previewBody.innerHTML = '';

                if (data.length === 0) return;

                // Add headers
                columns.forEach(column => {
                    const th = document.createElement('th');
                    th.textContent = column;
                    if (headerRow) headerRow.appendChild(th);
                });

                // Add data rows (first 10 only)
                const previewData = data.slice(0, 10);
                previewData.forEach(row => {
                    const tr = document.createElement('tr');
                    columns.forEach(column => {
                        const td = document.createElement('td');
                        td.textContent = row[column] || '';
                        if (tr) tr.appendChild(td);
                    });
                    if (previewBody) previewBody.appendChild(tr);
                });
            }

            // Utility function to format currency
            function formatCurrency(amount) {
                return amount.toLocaleString('en-US', {
                    style: 'currency',
                    currency: 'USD',
                    minimumFractionDigits: 2
                });
            }

            // Helper functions for UI states
            function showLoading() {
                if (loadingIndicator) loadingIndicator.classList.remove('hidden');
                if (errorMessage) errorMessage.classList.add('hidden');

                // Reset progress
                if (progressFill) progressFill.style.width = '0%';
                if (progressText) progressText.textContent = '0%';
                if (filesProcessed) filesProcessed.textContent = '0';
                if (currentBatch) currentBatch.textContent = '0';
                if (loadingText) loadingText.textContent = 'Initializing...';
            }

            function hideLoading() {
                if (loadingIndicator) loadingIndicator.classList.add('hidden');
                stopMemoryMonitoring();
            }

            function showError(message) {
                if (errorMessage) errorMessage.textContent = message;
                if (errorMessage) errorMessage.classList.remove('hidden');
                if (dashboardContainer) dashboardContainer.classList.add('hidden');
                stopMemoryMonitoring();
            }

            function resetDashboard() {
                // Stop any existing monitoring
                stopMemoryMonitoring();

                // Reset metrics
                if (totalFilesEl) totalFilesEl.textContent = '0';
                if (totalTransactionsEl) totalTransactionsEl.textContent = '0';
                if (totalAmountEl) totalAmountEl.textContent = '0';
                if (avgTransactionEl) avgTransactionEl.textContent = '0';

                // Reset summary
                if (highestTransactionEl) highestTransactionEl.textContent = '$0';
                if (lowestTransactionEl) lowestTransactionEl.textContent = '$0';
                if (commonTransactionTypeEl) commonTransactionTypeEl.textContent = 'N/A';
                if (commonPaymentMethodEl) commonPaymentMethodEl.textContent = 'N/A';
                if (dateRangeEl) dateRangeEl.textContent = 'N/A';
                if (uniqueCurrenciesEl) uniqueCurrenciesEl.textContent = '0';

                // Reset serial statistics
                if (totalSerialCountEl) totalSerialCountEl.textContent = '0';
                if (totalUniqueSerialCountEl) totalUniqueSerialCountEl.textContent = '0';
                if (totalCreditUniqueSerialEl) totalCreditUniqueSerialEl.textContent = '0';
                if (totalDebitUniqueSerialEl) totalDebitUniqueSerialEl.textContent = '0';

                // Reset progress and memory displays
                if (progressFill) progressFill.style.width = '0%';
                if (progressText) progressText.textContent = '0%';
                if (filesProcessed) filesProcessed.textContent = '0';
                if (currentBatch) currentBatch.textContent = '0';
                if (memoryUsage) memoryUsage.textContent = '0 MB';
                if (memoryUsage) memoryUsage.style.color = '#27ae60';
                if (loadingText) loadingText.textContent = 'Processing data...';

                // Reset file list and table
                // filesListEl.innerHTML = ''; // This element is removed
                if (headerRow) headerRow.innerHTML = '';
                if (previewBody) previewBody.innerHTML = '';

                // Hide dashboard
                if (dashboardContainer) dashboardContainer.classList.add('hidden');
                if (errorMessage) errorMessage.classList.add('hidden');

                // Reset data and force cleanup
                globalStats = {
                    totalFiles: 0,
                    totalTransactions: 0,
                    totalAmount: 0,
                    highestAmount: 0,
                    lowestAmount: Infinity,
                    transactionTypes: {},
                    paymentMethods: {},
                    currencies: new Set(),
                    dates: [],
                    allSerialNumbers: [],
                    uniqueSerialNumbers: new Set(),
                    creditSerialNumbers: new Set(),
                    debitSerialNumbers: new Set(),
                    sampleData: []
                };
                fileStats = [];
                forceGarbageCollection();
            }

            console.log('Transaction Dashboard ready!');
        });
    </script>
</body>

</html>
