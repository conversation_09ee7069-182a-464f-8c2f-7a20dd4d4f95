{"tabs": {"00": {"tab_id": "00", "cwd": "/workspace/html_template", "observer": {"block": "Terminal", "uuid": "4b407304-d577-4f04-8ce3-bb93daef1ded", "enable_llm_stream": false, "callback_url": ""}, "shell_command": ["bash"], "command_terminator": "\n"}}, "current_tab_id": "00", "forbidden_commands": {"run preview": "Use Deployer.deploy_to_public instead.", "serve ": "Use Deployer.deploy_to_public instead.", "python -m http.server": "Use python -u -m http.server port_number instead."}, "timeout": 300.0, "working_dir": "/workspace"}